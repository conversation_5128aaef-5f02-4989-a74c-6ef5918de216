{"name": "agentonboarding-api", "version": "1.0.0", "description": "Agent Onboarding API Service", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node scripts/setup.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo:all", "test": "jest", "test:watch": "jest --watch", "test-setup": "node test-setup.js"}, "keywords": ["express", "api", "agent", "onboarding", "sequelize", "postgresql"], "author": "Product Team", "license": "ISC", "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}